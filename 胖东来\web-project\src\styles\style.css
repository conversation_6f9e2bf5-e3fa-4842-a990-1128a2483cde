body {
    font-family: 'Microsoft YaHei', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

header {
    background: #35424a;
    color: #ffffff;
    padding: 10px 0;
    text-align: center;
}

h1 {
    margin: 0;
}

nav {
    margin: 20px 0;
    background-color: #2c3e50;
    border-radius: 5px;
    padding: 10px;
}

nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

nav li {
    margin: 0 10px;
}

nav a {
    color: #ffffff;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

nav a:hover {
    background: #e74c3c;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

main {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

section {
    background: white;
    margin-bottom: 30px;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h2 {
    color: #2c3e50;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

p {
    margin-bottom: 15px;
}

@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }
    nav li {
        margin: 5px 0;
    }
}

footer {
    background: #35424a;
    color: #ffffff;
    text-align: center;
    padding: 10px 0;
    position: relative;
    bottom: 0;
    width: 100%;
}
