<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推箱子小游戏 - <PERSON><PERSON>ban</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .game-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 800px;
            width: 100%;
        }

        .game-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .game-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.2rem;
            color: #34495e;
        }

        .info-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
        }

        .game-board {
            display: grid;
            gap: 2px;
            background: #34495e;
            border: 3px solid #2c3e50;
            border-radius: 10px;
            padding: 10px;
            margin: 0 auto;
            width: fit-content;
        }

        .cell {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            border-radius: 4px;
            transition: all 0.2s ease;
            position: relative;
        }

        .wall {
            background: #2c3e50;
            box-shadow: inset 2px 2px 4px rgba(0,0,0,0.3);
        }

        .floor {
            background: #ecf0f1;
        }

        .target {
            background: #f39c12;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.2);
        }

        .box {
            background: #e67e22;
            border: 2px solid #d35400;
            border-radius: 6px;
            box-shadow: 2px 2px 6px rgba(0,0,0,0.3);
        }

        .box-on-target {
            background: #27ae60;
            border: 2px solid #229954;
            box-shadow: 0 0 15px rgba(39, 174, 96, 0.5);
        }

        .player {
            background: #3498db;
            border: 2px solid #2980b9;
            border-radius: 50%;
            box-shadow: 2px 2px 6px rgba(0,0,0,0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .controls {
            margin-top: 30px;
            text-align: center;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 200px;
            margin: 20px auto;
        }

        .control-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: bold;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .control-btn:active {
            transform: translateY(0);
        }

        .up { grid-column: 2; }
        .left { grid-column: 1; grid-row: 2; }
        .right { grid-column: 3; grid-row: 2; }
        .down { grid-column: 2; grid-row: 2; }

        .game-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .game-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: bold;
        }

        .game-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .instructions {
            margin-top: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }

        .instructions h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .instructions ul {
            color: #34495e;
            line-height: 1.6;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .win-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            text-align: center;
            z-index: 1000;
            display: none;
        }

        .win-message.show {
            display: block;
            animation: bounceIn 0.5s ease;
        }

        @keyframes bounceIn {
            0% { transform: translate(-50%, -50%) scale(0.3); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.1); }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }

        .win-message h2 {
            color: #27ae60;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .win-message p {
            color: #34495e;
            font-size: 1.2rem;
            margin-bottom: 25px;
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 20px;
            }
            
            .game-title {
                font-size: 2rem;
            }
            
            .cell {
                width: 35px;
                height: 35px;
                font-size: 20px;
            }
            
            .game-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🎮 推箱子小游戏</h1>
            <div class="game-info">
                <div class="info-item">关卡: <span id="level">1</span></div>
                <div class="info-item">步数: <span id="steps">0</span></div>
                <div class="info-item">推箱次数: <span id="pushes">0</span></div>
            </div>
        </div>

        <div id="gameBoard" class="game-board"></div>

        <div class="controls">
            <div class="control-buttons">
                <button class="control-btn up" onclick="movePlayer('up')">↑</button>
                <button class="control-btn left" onclick="movePlayer('left')">←</button>
                <button class="control-btn down" onclick="movePlayer('down')">↓</button>
                <button class="control-btn right" onclick="movePlayer('right')">→</button>
            </div>

            <div class="game-buttons">
                <button class="game-btn" onclick="resetLevel()">🔄 重置关卡</button>
                <button class="game-btn" onclick="previousLevel()">⬅️ 上一关</button>
                <button class="game-btn" onclick="nextLevel()">➡️ 下一关</button>
            </div>
        </div>

        <div class="instructions">
            <h3>🎯 游戏说明</h3>
            <ul>
                <li>🔵 蓝色圆圈是玩家，可以用方向键或按钮控制移动</li>
                <li>📦 橙色方块是箱子，玩家可以推动但不能拉动</li>
                <li>🟡 黄色区域是目标位置，需要将所有箱子推到目标位置</li>
                <li>🟢 绿色方块表示箱子已正确放置在目标位置</li>
                <li>⚫ 黑色区域是墙壁，无法通过</li>
                <li>🏆 将所有箱子推到目标位置即可过关！</li>
            </ul>
        </div>
    </div>

    <div id="winMessage" class="win-message">
        <h2>🎉 恭喜过关！</h2>
        <p>您用了 <span id="finalSteps"></span> 步完成了关卡！</p>
        <button class="game-btn" onclick="nextLevel()">下一关</button>
        <button class="game-btn" onclick="closeWinMessage()">继续游戏</button>
    </div>

    <script>
        // 游戏状态
        let currentLevel = 0;
        let playerPos = { x: 0, y: 0 };
        let steps = 0;
        let pushes = 0;
        let gameBoard = [];
        
        // 游戏元素常量
        const WALL = '#';
        const FLOOR = ' ';
        const TARGET = '.';
        const BOX = '$';
        const PLAYER = '@';
        const BOX_ON_TARGET = '*';
        const PLAYER_ON_TARGET = '+';

        // 关卡数据
        const levels = [
            // 关卡1 - 简单入门
            [
                "########",
                "#      #",
                "#  .$  #",
                "#  @   #",
                "#      #",
                "########"
            ],
            // 关卡2 - 基础推箱
            [
                "########",
                "#   .  #",
                "#  $   #",
                "#  @   #",
                "#   $. #",
                "########"
            ],
            // 关卡3 - 稍有难度
            [
                "#########",
                "#       #",
                "# .$$.  #",
                "#   @   #",
                "#  .$$. #",
                "#       #",
                "#########"
            ]
        ];

        // 初始化游戏
        function initGame() {
            loadLevel(currentLevel);
            updateUI();
        }

        // 加载关卡
        function loadLevel(levelIndex) {
            if (levelIndex < 0 || levelIndex >= levels.length) return;

            const level = levels[levelIndex];
            gameBoard = [];

            // 解析关卡数据
            for (let y = 0; y < level.length; y++) {
                gameBoard[y] = [];
                for (let x = 0; x < level[y].length; x++) {
                    const char = level[y][x];
                    gameBoard[y][x] = char;

                    // 找到玩家初始位置
                    if (char === PLAYER || char === PLAYER_ON_TARGET) {
                        playerPos = { x, y };
                    }
                }
            }

            steps = 0;
            pushes = 0;
            renderBoard();
            updateUI();
        }

        // 渲染游戏板
        function renderBoard() {
            const boardElement = document.getElementById('gameBoard');
            boardElement.innerHTML = '';
            boardElement.style.gridTemplateColumns = `repeat(${gameBoard[0].length}, 40px)`;

            for (let y = 0; y < gameBoard.length; y++) {
                for (let x = 0; x < gameBoard[y].length; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';

                    const char = gameBoard[y][x];

                    // 设置背景
                    if (char === WALL) {
                        cell.classList.add('wall');
                    } else if (char === TARGET || char === PLAYER_ON_TARGET || char === BOX_ON_TARGET) {
                        cell.classList.add('target');
                    } else {
                        cell.classList.add('floor');
                    }

                    // 设置前景对象
                    if (char === PLAYER || char === PLAYER_ON_TARGET) {
                        cell.classList.add('player');
                    } else if (char === BOX) {
                        cell.classList.add('box');
                    } else if (char === BOX_ON_TARGET) {
                        cell.classList.add('box-on-target');
                    }

                    boardElement.appendChild(cell);
                }
            }
        }

        // 移动玩家
        function movePlayer(direction) {
            const directions = {
                up: { x: 0, y: -1 },
                down: { x: 0, y: 1 },
                left: { x: -1, y: 0 },
                right: { x: 1, y: 0 }
            };

            const dir = directions[direction];
            if (!dir) return;

            const newX = playerPos.x + dir.x;
            const newY = playerPos.y + dir.y;

            // 检查边界
            if (newY < 0 || newY >= gameBoard.length || newX < 0 || newX >= gameBoard[0].length) {
                return;
            }

            const targetCell = gameBoard[newY][newX];

            // 如果是墙，不能移动
            if (targetCell === WALL) {
                return;
            }

            // 如果是箱子，尝试推动
            if (targetCell === BOX || targetCell === BOX_ON_TARGET) {
                const boxNewX = newX + dir.x;
                const boxNewY = newY + dir.y;

                // 检查箱子新位置
                if (boxNewY < 0 || boxNewY >= gameBoard.length || boxNewX < 0 || boxNewX >= gameBoard[0].length) {
                    return;
                }

                const boxTargetCell = gameBoard[boxNewY][boxNewX];

                // 箱子不能推到墙或其他箱子上
                if (boxTargetCell === WALL || boxTargetCell === BOX || boxTargetCell === BOX_ON_TARGET) {
                    return;
                }

                // 推动箱子
                pushBox(newX, newY, boxNewX, boxNewY);
                pushes++;
            }

            // 移动玩家
            movePlayerTo(newX, newY);
            steps++;

            renderBoard();
            updateUI();

            // 检查是否获胜
            if (checkWin()) {
                setTimeout(showWinMessage, 300);
            }
        }

        // 推动箱子
        function pushBox(fromX, fromY, toX, toY) {
            const fromCell = gameBoard[fromY][fromX];
            const toCell = gameBoard[toY][toX];

            // 移除原位置的箱子
            if (fromCell === BOX) {
                gameBoard[fromY][fromX] = FLOOR;
            } else if (fromCell === BOX_ON_TARGET) {
                gameBoard[fromY][fromX] = TARGET;
            }

            // 在新位置放置箱子
            if (toCell === FLOOR) {
                gameBoard[toY][toX] = BOX;
            } else if (toCell === TARGET) {
                gameBoard[toY][toX] = BOX_ON_TARGET;
            }
        }

        // 移动玩家到指定位置
        function movePlayerTo(newX, newY) {
            const currentCell = gameBoard[playerPos.y][playerPos.x];
            const newCell = gameBoard[newY][newX];

            // 清除当前位置的玩家
            if (currentCell === PLAYER) {
                gameBoard[playerPos.y][playerPos.x] = FLOOR;
            } else if (currentCell === PLAYER_ON_TARGET) {
                gameBoard[playerPos.y][playerPos.x] = TARGET;
            }

            // 在新位置放置玩家
            if (newCell === FLOOR || newCell === BOX) {
                gameBoard[newY][newX] = PLAYER;
            } else if (newCell === TARGET || newCell === BOX_ON_TARGET) {
                gameBoard[newY][newX] = PLAYER_ON_TARGET;
            }

            playerPos = { x: newX, y: newY };
        }

        // 检查是否获胜
        function checkWin() {
            for (let y = 0; y < gameBoard.length; y++) {
                for (let x = 0; x < gameBoard[y].length; x++) {
                    const cell = gameBoard[y][x];
                    // 如果还有未放置在目标位置的箱子，游戏未结束
                    if (cell === BOX) {
                        return false;
                    }
                }
            }
            return true;
        }

        // 显示获胜消息
        function showWinMessage() {
            document.getElementById('finalSteps').textContent = steps;
            document.getElementById('winMessage').classList.add('show');
        }

        // 关闭获胜消息
        function closeWinMessage() {
            document.getElementById('winMessage').classList.remove('show');
        }

        // 更新UI
        function updateUI() {
            document.getElementById('level').textContent = currentLevel + 1;
            document.getElementById('steps').textContent = steps;
            document.getElementById('pushes').textContent = pushes;
        }

        // 重置关卡
        function resetLevel() {
            loadLevel(currentLevel);
        }

        // 下一关
        function nextLevel() {
            closeWinMessage();
            if (currentLevel < levels.length - 1) {
                currentLevel++;
                loadLevel(currentLevel);
            } else {
                alert('🎉 恭喜您完成了所有关卡！');
            }
        }

        // 上一关
        function previousLevel() {
            if (currentLevel > 0) {
                currentLevel--;
                loadLevel(currentLevel);
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    event.preventDefault();
                    movePlayer('up');
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    event.preventDefault();
                    movePlayer('down');
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    event.preventDefault();
                    movePlayer('left');
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    event.preventDefault();
                    movePlayer('right');
                    break;
                case 'r':
                case 'R':
                    resetLevel();
                    break;
                case 'n':
                case 'N':
                    nextLevel();
                    break;
                case 'p':
                case 'P':
                    previousLevel();
                    break;
            }
        });

        // 添加更多关卡
        levels.push(
            // 关卡4 - 中等难度
            [
                "##########",
                "#        #",
                "# .$  $. #",
                "#  $  $  #",
                "#   @    #",
                "#  $  $  #",
                "# .$  $. #",
                "#        #",
                "##########"
            ],
            // 关卡5 - 较难
            [
                "###########",
                "#    .    #",
                "#  $   $  #",
                "# $ . . $ #",
                "#  $ @ $  #",
                "# $ . . $ #",
                "#  $   $  #",
                "#    .    #",
                "###########"
            ],
            // 关卡6 - 挑战级
            [
                "############",
                "#          #",
                "#  .$$$$.  #",
                "#  $    $  #",
                "#  $ .@ $  #",
                "#  $    $  #",
                "#  .$$$.   #",
                "#          #",
                "############"
            ]
        );

        // 游戏初始化
        window.onload = function() {
            initGame();

            // 添加触摸支持
            let touchStartX = 0;
            let touchStartY = 0;

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
            });

            document.addEventListener('touchend', function(e) {
                if (!touchStartX || !touchStartY) return;

                const touchEndX = e.changedTouches[0].clientX;
                const touchEndY = e.changedTouches[0].clientY;

                const diffX = touchStartX - touchEndX;
                const diffY = touchStartY - touchEndY;

                if (Math.abs(diffX) > Math.abs(diffY)) {
                    if (diffX > 0) {
                        movePlayer('left');
                    } else {
                        movePlayer('right');
                    }
                } else {
                    if (diffY > 0) {
                        movePlayer('up');
                    } else {
                        movePlayer('down');
                    }
                }

                touchStartX = 0;
                touchStartY = 0;
            });
        };
    </script>
</body>
</html>
