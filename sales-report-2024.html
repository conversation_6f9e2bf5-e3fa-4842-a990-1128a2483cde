<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2024年上半年销售工作总结与下半年规划</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            background: white;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.5s ease;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            opacity: 1;
            transform: translateX(0);
        }

        .slide h1 {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 300;
        }

        .slide h2 {
            font-size: 2.5rem;
            color: #34495e;
            margin-bottom: 1.5rem;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .slide h3 {
            font-size: 1.8rem;
            color: #2980b9;
            margin: 1.5rem 0 1rem 0;
        }

        .slide p, .slide li {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 1rem;
        }

        .slide ul {
            margin-left: 2rem;
        }

        .slide li {
            margin-bottom: 0.8rem;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            margin-top: 10px;
            opacity: 0.9;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 20px;
            margin: 0 5px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1rem;
        }

        .title-slide {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .title-slide h1 {
            color: white;
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .title-slide .subtitle {
            font-size: 1.8rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .title-slide .author {
            font-size: 1.3rem;
            opacity: 0.8;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: 100%;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 30px 20px;
            }
            
            .slide h1 {
                font-size: 2rem;
            }
            
            .slide h2 {
                font-size: 1.8rem;
            }
            
            .two-column {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <div class="slide active title-slide">
            <h1>2024年上半年工作总结</h1>
            <div class="subtitle">暨下半年工作规划</div>
            <div class="author">
                <p>区域销售管理 | 河南·江苏·安徽</p>
                <p>罐头类·压缩饼干类产品</p>
                <p>2024年7月</p>
            </div>
        </div>

        <!-- 目录页 -->
        <div class="slide">
            <h2>汇报内容</h2>
            <div style="font-size: 1.5rem; line-height: 2.5;">
                <p>📊 <strong>01</strong> 上半年工作回顾</p>
                <p>📈 <strong>02</strong> 销售业绩分析</p>
                <p>🎯 <strong>03</strong> 市场开拓成果</p>
                <p>⚠️ <strong>04</strong> 存在问题与挑战</p>
                <p>🚀 <strong>05</strong> 下半年工作规划</p>
                <p>💡 <strong>06</strong> 具体实施措施</p>
            </div>
        </div>

        <!-- 上半年工作回顾 -->
        <div class="slide">
            <h2>01 上半年工作回顾</h2>
            <div class="two-column">
                <div>
                    <h3>🎯 主要工作内容</h3>
                    <ul>
                        <li>负责河南、江苏、安徽三省销售管理</li>
                        <li>罐头类产品线全面推广</li>
                        <li>压缩饼干类产品市场开拓</li>
                        <li>客户关系维护与新客户开发</li>
                        <li>销售团队建设与培训</li>
                    </ul>
                </div>
                <div>
                    <h3>📅 时间节点回顾</h3>
                    <ul>
                        <li><strong>1-2月：</strong>春节营销活动执行</li>
                        <li><strong>3-4月：</strong>新品推广与渠道建设</li>
                        <li><strong>5-6月：</strong>夏季销售旺季冲刺</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 销售业绩分析 -->
        <div class="slide">
            <h2>02 销售业绩分析</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">2,850万</span>
                    <div class="stat-label">总销售额</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">112%</span>
                    <div class="stat-label">目标完成率</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">156家</span>
                    <div class="stat-label">新增客户</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">98.5%</span>
                    <div class="stat-label">客户满意度</div>
                </div>
            </div>
            <div class="highlight">
                <h3>🏆 业绩亮点</h3>
                <ul>
                    <li>超额完成上半年销售目标12%</li>
                    <li>罐头类产品销售增长25%，压缩饼干增长18%</li>
                    <li>江苏市场表现突出，增长率达30%</li>
                </ul>
            </div>
        </div>

        <!-- 各省市场表现 -->
        <div class="slide">
            <h2>03 各省市场表现分析</h2>
            <div class="two-column">
                <div>
                    <h3>🏢 河南市场</h3>
                    <ul>
                        <li>销售额：980万元 (目标完成率：108%)</li>
                        <li>主力产品：午餐肉罐头、水果罐头</li>
                        <li>重点客户：大型超市连锁、批发商</li>
                        <li>增长亮点：压缩饼干在户外用品店渠道突破</li>
                    </ul>

                    <h3>🌊 江苏市场</h3>
                    <ul>
                        <li>销售额：1,200万元 (目标完成率：130%)</li>
                        <li>主力产品：海鲜罐头、军用压缩饼干</li>
                        <li>重点客户：电商平台、便利店连锁</li>
                        <li>增长亮点：线上销售渠道快速发展</li>
                    </ul>
                </div>
                <div>
                    <h3>🏔️ 安徽市场</h3>
                    <ul>
                        <li>销售额：670万元 (目标完成率：95%)</li>
                        <li>主力产品：肉类罐头、应急食品</li>
                        <li>重点客户：传统经销商、团购客户</li>
                        <li>待提升：市场渗透率有待加强</li>
                    </ul>

                    <div class="highlight">
                        <h3>📊 市场占有率</h3>
                        <div style="margin: 15px 0;">
                            <p>江苏省：15.2% <div class="progress-bar"><div class="progress-fill" style="width: 76%"></div></div></p>
                            <p>河南省：12.8% <div class="progress-bar"><div class="progress-fill" style="width: 64%"></div></div></p>
                            <p>安徽省：8.5% <div class="progress-bar"><div class="progress-fill" style="width: 42%"></div></div></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品线分析 -->
        <div class="slide">
            <h2>04 产品线销售分析</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">1,680万</span>
                    <div class="stat-label">罐头类产品销售额</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">1,170万</span>
                    <div class="stat-label">压缩饼干类销售额</div>
                </div>
            </div>

            <div class="two-column">
                <div>
                    <h3>🥫 罐头类产品表现</h3>
                    <ul>
                        <li><strong>午餐肉罐头：</strong>销售额520万，同比增长22%</li>
                        <li><strong>水果罐头：</strong>销售额480万，同比增长15%</li>
                        <li><strong>海鲜罐头：</strong>销售额380万，同比增长35%</li>
                        <li><strong>蔬菜罐头：</strong>销售额300万，同比增长8%</li>
                    </ul>
                    <div class="highlight">
                        <p><strong>优势：</strong>品质稳定，客户认知度高，复购率达85%</p>
                    </div>
                </div>
                <div>
                    <h3>🍪 压缩饼干类产品表现</h3>
                    <ul>
                        <li><strong>军用压缩饼干：</strong>销售额450万，同比增长28%</li>
                        <li><strong>户外压缩饼干：</strong>销售额380万，同比增长20%</li>
                        <li><strong>应急压缩饼干：</strong>销售额340万，同比增长12%</li>
                    </ul>
                    <div class="highlight">
                        <p><strong>机遇：</strong>户外运动兴起，应急储备意识增强，市场潜力巨大</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户开发成果 -->
        <div class="slide">
            <h2>05 客户开发与维护成果</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">156</span>
                    <div class="stat-label">新增客户数</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">892</span>
                    <div class="stat-label">活跃客户总数</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">85%</span>
                    <div class="stat-label">客户复购率</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">3.2万</span>
                    <div class="stat-label">平均客单价</div>
                </div>
            </div>

            <h3>🎯 重点客户开发成果</h3>
            <ul>
                <li><strong>大型连锁超市：</strong>成功签约华润万家、大润发等5家连锁超市</li>
                <li><strong>电商平台：</strong>入驻天猫、京东自营，月销售额突破200万</li>
                <li><strong>B2B客户：</strong>开发企业团购客户38家，包括大型工厂、学校食堂</li>
                <li><strong>新兴渠道：</strong>户外用品店、便利店等新渠道客户113家</li>
            </ul>

            <div class="highlight">
                <h3>🏆 客户服务亮点</h3>
                <p>建立客户服务微信群，7×24小时响应；定期客户回访，满意度达98.5%；推出客户积分奖励计划，提升客户粘性。</p>
            </div>
        </div>

        <!-- 存在问题与挑战 -->
        <div class="slide">
            <h2>06 存在问题与挑战</h2>
            <div class="two-column">
                <div>
                    <h3>⚠️ 主要问题</h3>
                    <ul>
                        <li><strong>安徽市场：</strong>目标完成率偏低，仅95%</li>
                        <li><strong>产品结构：</strong>高端产品占比不足，利润率有待提升</li>
                        <li><strong>渠道建设：</strong>线下渠道依赖度高，线上占比仅25%</li>
                        <li><strong>库存管理：</strong>部分产品库存周转率偏低</li>
                        <li><strong>团队建设：</strong>新员工培训周期长，成长速度待提升</li>
                    </ul>
                </div>
                <div>
                    <h3>🎯 面临挑战</h3>
                    <ul>
                        <li><strong>市场竞争：</strong>同类产品竞争激烈，价格战频发</li>
                        <li><strong>成本压力：</strong>原材料价格上涨，运输成本增加</li>
                        <li><strong>消费升级：</strong>消费者对产品品质要求不断提高</li>
                        <li><strong>政策影响：</strong>食品安全监管趋严，合规成本上升</li>
                        <li><strong>疫情影响：</strong>部分地区物流受限，影响配送效率</li>
                    </ul>
                </div>
            </div>

            <div class="highlight">
                <h3>💡 问题分析</h3>
                <p>主要问题集中在市场开拓不均衡、产品结构待优化、渠道建设需加强三个方面。需要在下半年重点解决，确保全年目标达成。</p>
            </div>
        </div>
    </div>

        <!-- 下半年工作目标 -->
        <div class="slide">
            <h2>07 下半年工作目标</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">3,200万</span>
                    <div class="stat-label">下半年销售目标</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">6,050万</span>
                    <div class="stat-label">全年销售目标</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">200家</span>
                    <div class="stat-label">新增客户目标</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">40%</span>
                    <div class="stat-label">线上销售占比目标</div>
                </div>
            </div>

            <div class="two-column">
                <div>
                    <h3>🎯 核心目标</h3>
                    <ul>
                        <li>完成下半年销售目标3,200万元</li>
                        <li>确保全年目标6,050万元达成</li>
                        <li>安徽市场销售额提升至800万以上</li>
                        <li>高端产品销售占比提升至35%</li>
                        <li>线上销售渠道占比达到40%</li>
                    </ul>
                </div>
                <div>
                    <h3>📈 增长策略</h3>
                    <ul>
                        <li>深耕现有市场，提升市场占有率</li>
                        <li>加大新产品推广力度</li>
                        <li>强化线上渠道建设</li>
                        <li>优化产品结构，提升利润率</li>
                        <li>加强团队建设，提升执行力</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 市场开拓规划 -->
        <div class="slide">
            <h2>08 市场开拓规划</h2>
            <div class="two-column">
                <div>
                    <h3>🏢 河南市场规划</h3>
                    <ul>
                        <li><strong>目标：</strong>销售额1,100万，增长12%</li>
                        <li><strong>策略：</strong>深化与现有大客户合作</li>
                        <li><strong>重点：</strong>开拓郑州、洛阳高端市场</li>
                        <li><strong>新品：</strong>推广有机罐头系列</li>
                    </ul>

                    <h3>🌊 江苏市场规划</h3>
                    <ul>
                        <li><strong>目标：</strong>销售额1,400万，增长17%</li>
                        <li><strong>策略：</strong>巩固线上优势，拓展线下</li>
                        <li><strong>重点：</strong>苏州、无锡市场深度开发</li>
                        <li><strong>新品：</strong>海鲜罐头高端系列</li>
                    </ul>
                </div>
                <div>
                    <h3>🏔️ 安徽市场规划</h3>
                    <ul>
                        <li><strong>目标：</strong>销售额800万，增长19%</li>
                        <li><strong>策略：</strong>加大投入，重点突破</li>
                        <li><strong>重点：</strong>合肥、芜湖市场全面布局</li>
                        <li><strong>新品：</strong>应急食品系列推广</li>
                    </ul>

                    <div class="highlight">
                        <h3>🚀 新市场机会</h3>
                        <ul>
                            <li>户外运动市场快速增长</li>
                            <li>应急储备需求增加</li>
                            <li>企业团购市场潜力巨大</li>
                            <li>直播带货等新营销模式</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品策略规划 -->
        <div class="slide">
            <h2>09 产品策略规划</h2>
            <div class="two-column">
                <div>
                    <h3>🥫 罐头类产品策略</h3>
                    <ul>
                        <li><strong>高端化：</strong>推出有机、无添加系列</li>
                        <li><strong>差异化：</strong>开发地方特色口味</li>
                        <li><strong>包装升级：</strong>环保包装，提升品牌形象</li>
                        <li><strong>渠道优化：</strong>针对不同渠道定制产品</li>
                    </ul>

                    <h3>🍪 压缩饼干策略</h3>
                    <ul>
                        <li><strong>功能化：</strong>运动营养、代餐系列</li>
                        <li><strong>口味多样：</strong>满足不同消费群体</li>
                        <li><strong>场景化：</strong>户外、应急、便携系列</li>
                        <li><strong>品质提升：</strong>改善口感和营养配比</li>
                    </ul>
                </div>
                <div>
                    <h3>📦 新品开发计划</h3>
                    <ul>
                        <li><strong>8月：</strong>有机水果罐头系列上市</li>
                        <li><strong>9月：</strong>运动营养压缩饼干发布</li>
                        <li><strong>10月：</strong>地方特色罐头试销</li>
                        <li><strong>11月：</strong>应急食品套装推出</li>
                    </ul>

                    <div class="highlight">
                        <h3>💰 价格策略</h3>
                        <p>高端产品采用价值定价，中端产品保持竞争优势，低端产品确保市场份额。通过产品组合优化，整体毛利率提升3-5%。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 渠道建设规划 -->
        <div class="slide">
            <h2>10 渠道建设规划</h2>
            <div class="two-column">
                <div>
                    <h3>🛒 线下渠道</h3>
                    <ul>
                        <li><strong>大型超市：</strong>新增10家连锁超市合作</li>
                        <li><strong>便利店：</strong>覆盖1000家便利店网点</li>
                        <li><strong>专业店：</strong>户外用品店、军品店拓展</li>
                        <li><strong>批发市场：</strong>强化传统批发渠道</li>
                    </ul>

                    <h3>💻 线上渠道</h3>
                    <ul>
                        <li><strong>电商平台：</strong>天猫、京东销售翻倍</li>
                        <li><strong>直播带货：</strong>每月2-3场直播活动</li>
                        <li><strong>社群营销：</strong>建立品牌粉丝群</li>
                        <li><strong>小程序：</strong>开发企业采购小程序</li>
                    </ul>
                </div>
                <div>
                    <h3>🤝 渠道合作模式</h3>
                    <ul>
                        <li><strong>独家代理：</strong>重点区域设立独家代理</li>
                        <li><strong>联合营销：</strong>与渠道商共同推广</li>
                        <li><strong>数据共享：</strong>建立销售数据共享机制</li>
                        <li><strong>培训支持：</strong>定期渠道商培训</li>
                    </ul>

                    <div class="highlight">
                        <h3>📊 渠道目标占比</h3>
                        <div style="margin: 15px 0;">
                            <p>线上渠道：40% <div class="progress-bar"><div class="progress-fill" style="width: 40%"></div></div></p>
                            <p>大型超市：30% <div class="progress-bar"><div class="progress-fill" style="width: 30%"></div></div></p>
                            <p>专业渠道：20% <div class="progress-bar"><div class="progress-fill" style="width: 20%"></div></div></p>
                            <p>其他渠道：10% <div class="progress-bar"><div class="progress-fill" style="width: 10%"></div></div></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 团队建设规划 -->
        <div class="slide">
            <h2>11 团队建设与管理提升</h2>
            <div class="two-column">
                <div>
                    <h3>👥 团队扩充计划</h3>
                    <ul>
                        <li><strong>销售人员：</strong>新增6名销售代表</li>
                        <li><strong>客服人员：</strong>增加2名客服专员</li>
                        <li><strong>市场推广：</strong>招聘1名市场专员</li>
                        <li><strong>数据分析：</strong>配置1名数据分析师</li>
                    </ul>

                    <h3>📚 培训发展计划</h3>
                    <ul>
                        <li><strong>产品培训：</strong>每月产品知识更新</li>
                        <li><strong>销售技能：</strong>季度销售技能提升</li>
                        <li><strong>客户服务：</strong>客户关系管理培训</li>
                        <li><strong>数字化工具：</strong>CRM系统使用培训</li>
                    </ul>
                </div>
                <div>
                    <h3>🎯 绩效管理优化</h3>
                    <ul>
                        <li><strong>目标设定：</strong>个人目标与团队目标结合</li>
                        <li><strong>过程管控：</strong>周报告、月总结制度</li>
                        <li><strong>激励机制：</strong>完善奖励和晋升体系</li>
                        <li><strong>文化建设：</strong>打造积极向上团队氛围</li>
                    </ul>

                    <div class="highlight">
                        <h3>💡 管理创新</h3>
                        <p>引入数字化管理工具，建立客户关系管理系统，实现销售过程可视化；推行OKR目标管理法，提升团队执行力和协作效率。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结与展望 -->
        <div class="slide title-slide">
            <h1>携手共进 再创佳绩</h1>
            <div class="subtitle">感谢领导支持 · 感谢团队努力</div>
            <div class="author">
                <p>🎯 目标明确 · 信心满满</p>
                <p>🚀 策略清晰 · 执行有力</p>
                <p>🏆 团结协作 · 共创辉煌</p>
            </div>
        </div>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">12</span>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prev-btn" onclick="previousSlide()">← 上一页</button>
        <button class="nav-btn" id="next-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // 更新导航按钮状态
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // 初始化
        showSlide(0);
    </script>
</body>
</html>
